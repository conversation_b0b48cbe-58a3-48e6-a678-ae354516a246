import logging
from datetime import timedelta
from django.utils import timezone
from django.core.mail import send_mail, EmailMultiAlternatives
from django.template.loader import render_to_string
from django.conf import settings
from celery import shared_task
from celery.exceptions import Retry

from .models import Notification
from events.models import Event
from authentication.models import UserProfile

logger = logging.getLogger(__name__)


@shared_task(bind=True, max_retries=3, default_retry_delay=60)
def send_meeting_created_email(self, event_id, user_id):
    """
    Toplantı oluşturulduğunda e-posta gönder
    """
    try:
        event = Event.objects.get(id=event_id)
        user_profile = UserProfile.objects.get(user_id=user_id)
        
        # E-posta içeriği hazırla
        subject = f"Yeni Toplantı: {event.title}"
        
        # HTML içerik
        html_content = render_to_string('emails/meeting_created.html', {
            'event': event,
            'user': user_profile.user,
            'meeting_url': event.meeting_url,
            'location': event.location,
            'start_time': event.start_datetime,
            'end_time': event.end_datetime,
        })
        
        # Metin içerik
        text_content = f"""
Merhaba {user_profile.user.first_name},

Yeni bir toplantı oluşturuldu:

Başlık: {event.title}
Tarih: {event.start_datetime.strftime('%d.%m.%Y %H:%M')}
{f'Bitiş: {event.end_datetime.strftime("%d.%m.%Y %H:%M")}' if event.end_datetime else ''}
{f'Lokasyon: {event.location}' if event.location else ''}
{f'Toplantı Linki: {event.meeting_url}' if event.meeting_url else ''}

{f'Açıklama: {event.description}' if event.description else ''}

İyi çalışmalar,
CRM Sistemi
        """
        
        # E-posta gönder
        email = EmailMultiAlternatives(
            subject=subject,
            body=text_content,
            from_email=settings.DEFAULT_FROM_EMAIL,
            to=[user_profile.user.email]
        )
        email.attach_alternative(html_content, "text/html")
        
        # Katılımcıların e-postalarını ekle
        participant_emails = []
        for contact in event.contacts.all():
            if contact.email:
                participant_emails.append(contact.email)
        
        if participant_emails:
            email.cc = participant_emails
        
        email.send()
        
        logger.info(f"Meeting created email sent for event {event_id} to user {user_id}")
        return f"Email sent successfully for event {event_id}"
        
    except Event.DoesNotExist:
        logger.error(f"Event {event_id} not found")
        return f"Event {event_id} not found"
    except UserProfile.DoesNotExist:
        logger.error(f"UserProfile for user {user_id} not found")
        return f"UserProfile for user {user_id} not found"
    except Exception as exc:
        logger.error(f"Error sending meeting created email: {exc}")
        if self.request.retries < self.max_retries:
            raise self.retry(countdown=60, exc=exc)
        return f"Failed to send email after {self.max_retries} retries: {exc}"


@shared_task(bind=True, max_retries=3, default_retry_delay=60)
def send_meeting_reminder_email(self, event_id, user_id, reminder_minutes=60):
    """
    Toplantı hatırlatması e-postası gönder
    """
    try:
        event = Event.objects.get(id=event_id)
        user_profile = UserProfile.objects.get(user_id=user_id)
        
        # Toplantı zamanı kontrolü
        time_until_meeting = event.start_datetime - timezone.now()
        if time_until_meeting.total_seconds() < 0:
            logger.warning(f"Meeting {event_id} has already started")
            return f"Meeting {event_id} has already started"
        
        # E-posta içeriği hazırla
        subject = f"Toplantı Hatırlatması: {event.title} ({reminder_minutes} dakika kaldı)"
        
        # HTML içerik
        html_content = render_to_string('emails/meeting_reminder.html', {
            'event': event,
            'user': user_profile.user,
            'reminder_minutes': reminder_minutes,
            'meeting_url': event.meeting_url,
            'location': event.location,
            'start_time': event.start_datetime,
            'time_until_meeting': time_until_meeting,
        })
        
        # Metin içerik
        text_content = f"""
Merhaba {user_profile.user.first_name},

Toplantı hatırlatması:

"{event.title}" toplantınız {reminder_minutes} dakika sonra başlayacak.

Tarih: {event.start_datetime.strftime('%d.%m.%Y %H:%M')}
{f'Lokasyon: {event.location}' if event.location else ''}
{f'Toplantı Linki: {event.meeting_url}' if event.meeting_url else ''}

Lütfen zamanında katılmayı unutmayın.

İyi çalışmalar,
CRM Sistemi
        """
        
        # E-posta gönder
        email = EmailMultiAlternatives(
            subject=subject,
            body=text_content,
            from_email=settings.DEFAULT_FROM_EMAIL,
            to=[user_profile.user.email]
        )
        email.attach_alternative(html_content, "text/html")
        email.send()
        
        logger.info(f"Meeting reminder email sent for event {event_id} to user {user_id}")
        return f"Reminder email sent successfully for event {event_id}"
        
    except Event.DoesNotExist:
        logger.error(f"Event {event_id} not found")
        return f"Event {event_id} not found"
    except UserProfile.DoesNotExist:
        logger.error(f"UserProfile for user {user_id} not found")
        return f"UserProfile for user {user_id} not found"
    except Exception as exc:
        logger.error(f"Error sending meeting reminder email: {exc}")
        if self.request.retries < self.max_retries:
            raise self.retry(countdown=60, exc=exc)
        return f"Failed to send reminder email after {self.max_retries} retries: {exc}"


@shared_task
def send_pending_meeting_reminders():
    """
    Bekleyen toplantı hatırlatmalarını gönder
    """
    now = timezone.now()
    
    # Gönderilmemiş ve zamanı gelmiş bildirimleri bul
    pending_notifications = Notification.objects.filter(
        notification_type='meeting_reminder',
        scheduled_time__lte=now,
        is_sent=False
    )
    
    sent_count = 0
    for notification in pending_notifications:
        try:
            if notification.content_object and notification.content_object.assigned_to:
                # E-posta gönder
                send_meeting_reminder_email.delay(
                    notification.content_object.id,
                    notification.content_object.assigned_to.id,
                    notification.metadata.get('reminder_minutes', 60)
                )
                
                # Bildirimi gönderildi olarak işaretle
                notification.mark_as_sent()
                sent_count += 1
                
        except Exception as e:
            logger.error(f"Error processing notification {notification.id}: {e}")
    
    logger.info(f"Processed {sent_count} meeting reminder notifications")
    return f"Sent {sent_count} meeting reminder emails"


@shared_task
def send_pending_email_reminders():
    """
    Bekleyen e-posta hatırlatmalarını gönder
    """
    now = timezone.now()
    
    # Gönderilmemiş ve zamanı gelmiş e-posta hatırlatmalarını bul
    pending_notifications = Notification.objects.filter(
        notification_type__in=['meeting_created', 'event_updated'],
        scheduled_time__lte=now,
        is_sent=False
    )
    
    sent_count = 0
    for notification in pending_notifications:
        try:
            if notification.content_object and notification.content_object.assigned_to:
                if notification.notification_type == 'meeting_created':
                    send_meeting_created_email.delay(
                        notification.content_object.id,
                        notification.content_object.assigned_to.id
                    )
                
                # Bildirimi gönderildi olarak işaretle
                notification.mark_as_sent()
                sent_count += 1
                
        except Exception as e:
            logger.error(f"Error processing email notification {notification.id}: {e}")
    
    logger.info(f"Processed {sent_count} email reminder notifications")
    return f"Sent {sent_count} email reminder notifications"


@shared_task
def cleanup_old_notifications():
    """
    Eski bildirimleri temizle (30 günden eski okunmuş bildirimler)
    """
    cutoff_date = timezone.now() - timedelta(days=30)
    
    # 30 günden eski okunmuş bildirimleri sil
    deleted_count = Notification.objects.filter(
        is_read=True,
        read_at__lt=cutoff_date
    ).delete()[0]
    
    logger.info(f"Cleaned up {deleted_count} old notifications")
    return f"Cleaned up {deleted_count} old notifications"


@shared_task(bind=True, max_retries=3)
def send_bulk_notification_emails(self, notification_ids):
    """
    Toplu bildirim e-postaları gönder
    """
    try:
        notifications = Notification.objects.filter(id__in=notification_ids)
        sent_count = 0
        
        for notification in notifications:
            try:
                if notification.recipient.email:
                    send_mail(
                        subject=notification.title,
                        message=notification.message,
                        from_email=settings.DEFAULT_FROM_EMAIL,
                        recipient_list=[notification.recipient.email],
                        fail_silently=False
                    )
                    notification.mark_as_sent()
                    sent_count += 1
            except Exception as e:
                logger.error(f"Error sending email for notification {notification.id}: {e}")
        
        logger.info(f"Sent {sent_count} bulk notification emails")
        return f"Sent {sent_count} emails successfully"
        
    except Exception as exc:
        logger.error(f"Error in bulk email sending: {exc}")
        if self.request.retries < self.max_retries:
            raise self.retry(countdown=60, exc=exc)
        return f"Failed to send bulk emails after {self.max_retries} retries: {exc}"
