from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
from django.contrib.auth.models import User
from events.models import Event
from .models import Notification, NotificationPreference


@receiver(post_save, sender=Event)
def create_event_notifications(sender, instance, created, **kwargs):
    """
    Etkinlik oluşturulduğunda veya güncellendiğinde bildirim oluştur
    """
    if created:
        # Yeni etkinlik oluşturuldu
        if instance.event_type == 'meeting':
            # Toplantı oluşturuldu bildirimi
            if instance.assigned_to:
                Notification.create_meeting_created(instance, instance.assigned_to)
            
            # Katılımcılara bildirim gönder
            for contact in instance.contacts.all():
                # Eğer contact'ın bir user'ı varsa bildirim gönder
                # Bu kısım contact modelinde user field'ı varsa çalışır
                pass
            
            # Toplantı hatırlatması oluştur
            if instance.assigned_to:
                # Kullanıcının bildirim tercihlerini al
                try:
                    prefs = instance.assigned_to.notification_preferences
                    reminder_minutes = getattr(prefs, 'meeting_reminder_minutes', 60)
                except NotificationPreference.DoesNotExist:
                    reminder_minutes = 60
                
                Notification.create_meeting_reminder(
                    instance, 
                    instance.assigned_to, 
                    reminder_minutes
                )
    else:
        # Etkinlik güncellendi
        if instance.assigned_to:
            Notification.objects.create(
                recipient=instance.assigned_to,
                notification_type='event_updated',
                priority='medium',
                title=f"Etkinlik Güncellendi: {instance.title}",
                message=f"'{instance.title}' etkinliği güncellendi.",
                content_object=instance,
                action_url=f"/events/{instance.id}",
                metadata={
                    'event_id': instance.id,
                    'event_type': instance.event_type,
                }
            )


@receiver(post_delete, sender=Event)
def create_event_deletion_notification(sender, instance, **kwargs):
    """
    Etkinlik silindiğinde bildirim oluştur
    """
    if instance.assigned_to:
        Notification.objects.create(
            recipient=instance.assigned_to,
            notification_type='event_cancelled',
            priority='high',
            title=f"Etkinlik İptal Edildi: {instance.title}",
            message=f"'{instance.title}' etkinliği iptal edildi/silindi.",
            metadata={
                'event_id': instance.id,
                'event_type': instance.event_type,
                'cancelled_at': instance.updated_at.isoformat() if instance.updated_at else None,
            }
        )


@receiver(post_save, sender=User)
def create_notification_preferences(sender, instance, created, **kwargs):
    """
    Yeni kullanıcı oluşturulduğunda varsayılan bildirim tercihlerini oluştur
    """
    if created:
        NotificationPreference.objects.get_or_create(
            user=instance,
            defaults={
                'email_reminders': True,
                'email_events': True,
                'email_tasks': True,
                'email_system': True,
                'web_reminders': True,
                'web_events': True,
                'web_tasks': True,
                'web_system': True,
            }
        )
