@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

.ai-preview-contetnt

/* Tüm input, textarea ve select alanlarını beyaz zemin + siyah yazı yap */
input,
textarea,
select {
  background-color: #ffffff !important;
  color: #000000 !important;
  border: 1px solid #ccc;
  padding: 0.5rem;
  border-radius: 0.375rem;
  font-size: 1rem;
}

/* Placeholder yazılar da siyah olsun */
input::placeholder,
textarea::placeholder {
  color: #000000 !important;
}

/* Focus durumunda kenarlık değişikliği */
input:focus,
textarea:focus,
select:focus {
  outline: none;
  border-color: #888;
}

/* Rich Text Editor Styles */
.rich-text-editor [contenteditable] {
  outline: none;
  min-height: 200px;
  padding: 12px;
  line-height: 1.5;
  font-family: inherit;
  font-size: 14px;
  color: #000000 !important;
  background-color: #ffffff !important;
  direction: ltr;
  text-align: left;
}

.rich-text-editor [contenteditable]:empty:before {
  content: attr(data-placeholder);
  color: #666666;
  pointer-events: none;
  font-style: italic;
  position: absolute;
}

.rich-text-editor [contenteditable]:focus {
  outline: none;
  box-shadow: 0 0 0 2px #6366f1;
}

/* Rich text content styles */
.rich-text-editor [contenteditable] strong,
.rich-text-editor [contenteditable] b {
  font-weight: bold;
}

.rich-text-editor [contenteditable] em,
.rich-text-editor [contenteditable] i {
  font-style: italic;
}

.rich-text-editor [contenteditable] u {
  text-decoration: underline;
}

.rich-text-editor [contenteditable] ul {
  list-style-type: disc;
  margin-left: 20px;
  margin-top: 8px;
  margin-bottom: 8px;
}

.rich-text-editor [contenteditable] ol {
  list-style-type: decimal;
  margin-left: 20px;
  margin-top: 8px;
  margin-bottom: 8px;
}

.rich-text-editor [contenteditable] li {
  margin-bottom: 4px;
}

/* Rich text display styles */
.rich-text-display {
  line-height: 1.6;
  color: #374151;
  direction: ltr;
  text-align: left;
}

.rich-text-display strong,
.rich-text-display b {
  font-weight: bold;
}

.rich-text-display em,
.rich-text-display i {
  font-style: italic;
}

.rich-text-display u {
  text-decoration: underline;
}

.rich-text-display ul {
  list-style-type: disc;
  margin-left: 20px;
  margin-top: 8px;
  margin-bottom: 8px;
}

.rich-text-display ol {
  list-style-type: decimal;
  margin-left: 20px;
  margin-top: 8px;
  margin-bottom: 8px;
}

.rich-text-display li {
  margin-bottom: 4px;
}

/* TinyMCE Özelleştirmeleri */
.tinymce-wrapper {
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.tinymce-wrapper .tox-tinymce {
  border: 0 !important;
  border-radius: 0.375rem;
}

.tinymce-wrapper .tox-toolbar-overlord {
  border-bottom: 1px solid #e5e7eb;
}

.tinymce-wrapper .tox-editor-header {
  background-color: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.tinymce-wrapper .tox-statusbar {
  border-top: 1px solid #e5e7eb;
  background-color: #f9fafb;
}

/* TinyMCE Türkçe karakter desteği */
.tox-edit-area__iframe {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif !important;
}

/* TinyMCE içerik alanı stilleri */
.tox .tox-edit-area {
  background-color: #ffffff !important;
}

.tox .tox-edit-area__iframe {
  background-color: #ffffff !important;
}
